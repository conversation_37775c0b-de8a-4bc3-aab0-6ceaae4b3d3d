<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">

<h:head>
    <title>Confirmação de Senha - Portal de Clientes</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="assets/css/font-awesome.min.css"/>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Open Sans', sans-serif;
        }
        .confirmation-container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .confirmation-header {
            background: #3c8dbc;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .confirmation-body {
            padding: 40px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-control {
            height: 45px;
            border-radius: 5px;
            border: 1px solid #ddd;
            padding: 0 15px;
        }
        .btn-confirm {
            background: #3c8dbc;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            width: 100%;
            font-size: 16px;
            font-weight: bold;
        }
        .btn-confirm:hover {
            background: #2c6aa0;
        }
        .alert {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</h:head>

<h:body>
    <f:metadata>
        <f:viewParam name="chave" value="#{confirmarSenha.chaveAcesso}"/>
        <f:viewAction action="#{confirmarSenha.inicializar}"/>
    </f:metadata>

    <div class="confirmation-container">
        <div class="confirmation-header">
            <h2><i class="fa fa-key"></i> Confirmação de Cadastro</h2>
            <p>Portal de Clientes SatMOB</p>
        </div>
        
        <div class="confirmation-body">
            <h:form id="formConfirmacao">
                <p:messages id="msgs" showDetail="true" closable="true"/>
                
                <h:panelGroup rendered="#{not confirmarSenha.senhaConfirmada}">
                    <div class="form-group">
                        <label for="chaveAcesso">Chave de Acesso:</label>
                        <p:inputText id="chaveAcesso" 
                                     value="#{confirmarSenha.chaveAcesso}" 
                                     styleClass="form-control"
                                     placeholder="Digite sua chave de acesso"
                                     required="true"
                                     requiredMessage="Chave de acesso é obrigatória"
                                     disabled="true"/>
                    </div>
                    
                    <div class="form-group">
                        <label for="senhaTemporaria">Senha Temporária:</label>
                        <p:password id="senhaTemporaria" 
                                    value="#{confirmarSenha.senhaTemporaria}" 
                                    styleClass="form-control"
                                    placeholder="Digite sua senha temporária"
                                    required="true"
                                    requiredMessage="Senha temporária é obrigatória"/>
                    </div>
                    
                    <div class="form-group">
                        <label for="novaSenha">Nova Senha:</label>
                        <p:password id="novaSenha" 
                                    value="#{confirmarSenha.novaSenha}" 
                                    styleClass="form-control"
                                    placeholder="Digite sua nova senha"
                                    required="true"
                                    requiredMessage="Nova senha é obrigatória"/>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmarNovaSenha">Confirmar Nova Senha:</label>
                        <p:password id="confirmarNovaSenha" 
                                    value="#{confirmarSenha.confirmarNovaSenha}" 
                                    styleClass="form-control"
                                    placeholder="Confirme sua nova senha"
                                    required="true"
                                    requiredMessage="Confirmação de senha é obrigatória"/>
                    </div>
                    
                    <p:commandButton value="Confirmar Cadastro" 
                                     action="#{confirmarSenha.confirmarSenha}"
                                     update="formConfirmacao"
                                     styleClass="btn-confirm"
                                     icon="fa fa-check"/>
                </h:panelGroup>
                
                <h:panelGroup rendered="#{confirmarSenha.senhaConfirmada}">
                    <div class="alert alert-success">
                        <h4><i class="fa fa-check-circle"></i> Cadastro Confirmado com Sucesso!</h4>
                        <p>Sua senha foi definida com sucesso. Agora você pode acessar o Portal de Clientes com sua nova senha.</p>
                        <p><strong>Próximos passos:</strong></p>
                        <ul>
                            <li>Acesse o Portal de Clientes</li>
                            <li>Use sua chave de acesso e nova senha para fazer login</li>
                        </ul>
                    </div>
                    
                    <p:commandButton value="Ir para Login" 
                                     action="#{confirmarSenha.irParaLogin}"
                                     styleClass="btn-confirm"
                                     icon="fa fa-sign-in"/>
                </h:panelGroup>
            </h:form>
        </div>
    </div>
</h:body>
</html>

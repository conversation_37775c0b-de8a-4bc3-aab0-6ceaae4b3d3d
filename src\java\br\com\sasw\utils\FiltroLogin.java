/*
 */
package br.com.sasw.utils;

import SasBeans.PessoaLogin;
import java.io.IOException;
import java.util.List;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 *
 * <AUTHOR>
 */
@WebFilter("*.xhtml")
public class FiltroLogin implements Filter {

    private static final String AJAX_REDIRECT_XML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
            + "<partial-response><redirect url=\"%s\"></redirect></partial-response>";

    @Override
    public void init(FilterConfig config) throws ServletException {
        // If you have any <init-param> in web.xml, then you could get them
        // here by config.getInitParameter("name") and assign it as field.
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;
        HttpServletRequest request = (HttpServletRequest) req;
        HttpSession session = request.getSession(false);
        List<PessoaLogin> empresas = null;
        boolean ajaxRequest = "partial/ajax".equals(request.getHeader("Faces-Request"));

        String caminho = request.getRequestURI();
        String loginURL = request.getContextPath() + "/index.xhtml?msg=SessaoExpirada", kb = "false";

        if (caminho.contains("SatMobWeb/msl/")) {
            loginURL = request.getContextPath() + "/msl/index.xhtml?msg=SessaoExpirada";
        }

        String nivel, filial;
        try {
            empresas = (List<PessoaLogin>) session.getAttribute("empresas");
        } catch (Exception e) {
        }
        try {
            filial = (String) session.getAttribute("filial");
        } catch (Exception e) {
            filial = null;
        }
        try {
            nivel = (String) session.getAttribute("nivel");
            if (nivel == null) {
                nivel = "0";
            }
        } catch (Exception e) {
            nivel = "0";
        }
        try {
            kb = (String) session.getAttribute("kb");
        } catch (Exception e) {
        }
        if (nivel.equals("4")
                && !request.getRequestURI().endsWith("/recursoshumanos/portalrh.xhtml")
                && !request.getRequestURI().contains("/javax.faces.resource/")) {
            response.sendRedirect(request.getContextPath() + "/portalrh.xhtml");
        } // Se o nível for GTV e o redirecionamento não for para página de usuários, redirecionar de volta.
        else if (nivel.equals("5")
                && !request.getRequestURI().endsWith("/guia/guiasclientes.xhtml")
                && !request.getRequestURI().endsWith("/guia/pedidos.xhtml")
                && !request.getRequestURI().endsWith("/operacoes/pedidos.xhtml")
                && !request.getRequestURI().endsWith("/cofre/cofre.xhtml")
                && !request.getRequestURI().endsWith("/relatorio/prefatura.xhtml")                
                && !request.getRequestURI().endsWith("/relatorio/prefaturaclifat.xhtml")
                && !request.getRequestURI().endsWith("/configuracoes/acessos.xhtml")
                && !request.getRequestURI().endsWith("/tesouraria/mapa_tesouraria.xhtml")
                && !request.getRequestURI().contains("/javax.faces.resource/")) {
            if (!request.getRequestURI().endsWith("/menu_cliente.xhtml")) {
                //response.sendRedirect(request.getContextPath() + "/guia/guiasclientes.xhtml");
                response.sendRedirect(request.getContextPath() + "/menu_cliente.xhtml");
            }
            else{
                chain.doFilter(req, res);
            }
        } else if ((nivel.equals("6"))
                && !(request.getRequestURI().endsWith("/cofre/cofres.xhtml")
                || request.getRequestURI().endsWith("/cofre/dashboard_geral.xhtml")
                || request.getRequestURI().endsWith("/cofre/cofres_movimentacao.xhtml")
                || request.getRequestURI().endsWith("/cofre/cofre.xhtml")
                || request.getRequestURI().endsWith("/cofre/movimentacao.xhtml"))
                && !request.getRequestURI().endsWith("/configuracoes/acessos.xhtml")
                && !request.getRequestURI().contains("/javax.faces.resource/")) {
            response.sendRedirect(request.getContextPath() + "/cofre/cofre.xhtml");
        } else if (nivel.equals("8")
                && !(request.getRequestURI().endsWith("/relatorio/portal.xhtml"))
                && !request.getRequestURI().contains("/javax.faces.resource/")) {
            response.sendRedirect(request.getContextPath() + "/relatorio/portal.xhtml");
        } else if (nivel.equals("10")
                && !(request.getRequestURI().endsWith("/SaswChamados/MenuSaswChamados.xhtml"))
                && !request.getRequestURI().contains("/javax.faces.resource/")) {
            response.sendRedirect(request.getContextPath() + "/SaswChamados/MenuSaswChamados.xhtml");
        } else if (kb != null
                && kb.equals("false")//
                && request.getRequestURI().endsWith("/kanbansatellite.xhtml")
                && !request.getRequestURI().contains("/javax.faces.resource/")
                && ajaxRequest) {
            response.setContentType("text/xml");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().printf(AJAX_REDIRECT_XML, request.getContextPath() + "/kanbansatellite.xhtml?msg=SessaoExpirada"); // So, return special XML response instructing JSF ajax to send a redirect.
        } else if (empresas == null
                && !request.getRequestURI().endsWith("/index.xhtml")
                && !request.getRequestURI().endsWith("/login.xhtml")
                && !request.getRequestURI().endsWith("/loginSatDesk.xhtml")
                && !request.getRequestURI().endsWith("/ponto_mob.xhtml")
                && !request.getRequestURI().endsWith("/portal_rh.xhtml")
                && !request.getRequestURI().endsWith("/ponto_mob_historico.xhtml")
                && !request.getRequestURI().endsWith("/inspecao_mob.xhtml")
                && !request.getRequestURI().endsWith("/qrCode.html")
                && !request.getRequestURI().endsWith("/kanbansatellite.xhtml")
                && !request.getRequestURI().endsWith("/primeiroacesso.xhtml")
                && !request.getRequestURI().endsWith("/arquivos.xhtml")
                && !request.getRequestURI().endsWith("/recursoshumanos/candidato.xhtml")
                && !request.getRequestURI().endsWith("/confirmar-senha.xhtml")
                && !request.getRequestURI().contains("/javax.faces.resource/")
                && ajaxRequest) {
            response.setContentType("text/xml");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().printf(AJAX_REDIRECT_XML, loginURL); // So, return special XML response instructing JSF ajax to send a redirect.
        } else if (empresas == null
                && !request.getRequestURI().endsWith("/index.xhtml")
                && !request.getRequestURI().endsWith("/login.xhtml")
                && !request.getRequestURI().endsWith("/loginSatDesk.xhtml")
                && !request.getRequestURI().endsWith("/ponto_mob.xhtml")
                && !request.getRequestURI().endsWith("/portal_rh.xhtml")
                && !request.getRequestURI().endsWith("/ponto_mob_historico.xhtml")
                && !request.getRequestURI().endsWith("/inspecao_mob.xhtml")
                && !request.getRequestURI().endsWith("/qrCode.html")
                && !request.getRequestURI().endsWith("/kanbansatellite.xhtml")
                && !request.getRequestURI().endsWith("/primeiroacesso.xhtml")
                && !request.getRequestURI().endsWith("/arquivos.xhtml")
                && !request.getRequestURI().endsWith("/recursoshumanos/candidato.xhtml")
                && !request.getRequestURI().endsWith("/guia_satmob.xhtml")
                && !request.getRequestURI().endsWith("/confirmar-senha.xhtml")
                && !request.getRequestURI().contains("/javax.faces.resource/")) {
            if (!caminho.contains("SatMobWeb/msl/")) {
                response.sendRedirect(request.getContextPath() + "/index.xhtml");
            } else {
                response.sendRedirect(request.getContextPath() + "/msl/index.xhtml");
            }
        } else if (filial == null
                && !request.getRequestURI().endsWith("/index.xhtml")
                && !request.getRequestURI().endsWith("/login.xhtml")
                && !request.getRequestURI().endsWith("/loginSatDesk.xhtml")
                && !request.getRequestURI().endsWith("/ponto_mob.xhtml")
                && !request.getRequestURI().endsWith("/portal_rh.xhtml")
                && !request.getRequestURI().endsWith("/ponto_mob_historico.xhtml")
                && !request.getRequestURI().endsWith("/inspecao_mob.xhtml")
                && !request.getRequestURI().endsWith("/qrCode.html")
                && !request.getRequestURI().endsWith("/param.xhtml")
                && !request.getRequestURI().endsWith("/mapa.xhtml")
                && !request.getRequestURI().endsWith("/container.xhtml")
                && !request.getRequestURI().endsWith("/kanbansatellite.xhtml")
                && !request.getRequestURI().endsWith("/primeiroacesso.xhtml")
                && !request.getRequestURI().endsWith("/recursoshumanos/candidato.xhtml")
                && !request.getRequestURI().endsWith("/arquivos.xhtml")
                && !request.getRequestURI().endsWith("/guia_satmob.xhtml")
                && !request.getRequestURI().endsWith("/confirmar-senha.xhtml")
                && !request.getRequestURI().contains("/javax.faces.resource/")) {
            response.sendRedirect(request.getContextPath() + "/param.xhtml");
        } else {
            chain.doFilter(req, res);
        }
    }

    @Override
    public void destroy() {
        // If you have assigned any expensive resources as field of
        // this Filter class, then you could clean/close them here.
    }

}
